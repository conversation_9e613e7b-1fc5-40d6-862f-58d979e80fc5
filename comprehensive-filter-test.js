/**
 * Comprehensive test to verify the filter fix is working correctly
 */

async function comprehensiveFilterTest() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Comprehensive Filter Fix Test...\n');
  
  try {
    // Test 1: Search for "dental" in device name
    console.log('1️⃣ Testing search for "dental"...');
    const searchResponse = await fetch(`${baseUrl}/api/data/us_pmn?allFields=dental&page=1&limit=20`);
    const searchResult = await searchResponse.json();
    
    if (searchResult.success) {
      console.log(`✅ Search results: ${searchResult.totalCount} records found`);
    } else {
      console.log('❌ Search failed:', searchResult.error);
      return;
    }
    
    // Test 2: Get dynamic counts for "reviewadvisecomm" with "dental" search applied
    console.log('\n2️⃣ Testing dynamic counts for reviewadvisecomm with search context...');
    const filters = { allFields: 'dental' };
    const params = new URLSearchParams({
      field: 'reviewadvisecomm',
      filters: JSON.stringify(filters)
    });
    
    const countsResponse = await fetch(`${baseUrl}/api/meta/us_pmn/dynamic-counts?${params}`);
    const countsResult = await countsResponse.json();
    
    if (countsResult.success) {
      console.log('✅ Dynamic counts with search context:');
      if (countsResult.data && Array.isArray(countsResult.data)) {
        console.log(`   Found ${countsResult.data.length} different values:`);
        countsResult.data.slice(0, 5).forEach(item => {
          console.log(`   ${item.value}: ${item.count} records`);
        });
        
        // Verify that the counts are based on search results, not entire dataset
        const totalFilteredCount = countsResult.data.reduce((sum, item) => sum + item.count, 0);
        console.log(`   Total filtered count: ${totalFilteredCount}`);
        
        if (totalFilteredCount > 0 && totalFilteredCount <= searchResult.totalCount) {
          console.log('✅ Filter counts are correctly based on search results!');
        } else if (totalFilteredCount === 0) {
          console.log('⚠️ No results found');
        } else {
          console.log('❌ Filter counts seem to be based on entire dataset, not search results');
          console.log(`   Expected <= ${searchResult.totalCount}, got ${totalFilteredCount}`);
        }
      } else {
        console.log('❌ No data array in response');
      }
    } else {
      console.log('❌ Dynamic counts failed:', countsResult.error);
    }
    
    // Test 3: Compare with full dataset counts
    console.log('\n3️⃣ Comparing with full dataset counts...');
    const fullParams = new URLSearchParams({
      field: 'reviewadvisecomm',
      filters: JSON.stringify({}) // No filters
    });
    
    const fullCountsResponse = await fetch(`${baseUrl}/api/meta/us_pmn/dynamic-counts?${fullParams}`);
    const fullCountsResult = await fullCountsResponse.json();
    
    if (fullCountsResult.success && fullCountsResult.data) {
      const fullTotalCount = fullCountsResult.data.reduce((sum, item) => sum + item.count, 0);
      console.log(`   Full dataset total: ${fullTotalCount}`);
      
      if (countsResult.success && countsResult.data) {
        const filteredTotalCount = countsResult.data.reduce((sum, item) => sum + item.count, 0);
        console.log(`   Filtered dataset total: ${filteredTotalCount}`);
        
        if (filteredTotalCount < fullTotalCount) {
          console.log('✅ Progressive filtering is working correctly!');
        } else if (filteredTotalCount === 0) {
          console.log('⚠️ No matching records in filtered dataset');
        } else {
          console.log('❌ Filtering may not be working properly');
        }
      }
    }
    
    console.log('\n🎉 Comprehensive filter test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
comprehensiveFilterTest();
