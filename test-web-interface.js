/**
 * Test the web interface to verify the filter fix
 */

async function testWebInterface() {
  console.log('🌐 Testing Web Interface Filter Fix...\n');
  
  try {
    // Test the actual page that the user mentioned
    console.log('1️⃣ Testing the actual page: /data/list/us_pmn');
    
    // First, let's test a simple search without filters
    console.log('\n2️⃣ Testing simple search API...');
    const searchResponse = await fetch('http://localhost:3000/api/data/us_pmn?allFields=dental&page=1&limit=10');
    const searchResult = await searchResponse.json();
    
    console.log('Search API Response:', {
      success: searchResult.success,
      totalCount: searchResult.totalCount,
      dataLength: searchResult.data?.length,
      error: searchResult.error
    });
    
    if (searchResult.success && searchResult.data && searchResult.data.length > 0) {
      console.log('✅ Search is working!');
      console.log('Sample record:', {
        knumber: searchResult.data[0].knumber,
        devicename: searchResult.data[0].devicename?.substring(0, 50) + '...',
        thirdparty: searchResult.data[0].thirdparty,
        reviewadvisecomm: searchResult.data[0].reviewadvisecomm
      });
      
      // Now test the dynamic counts with the search context
      console.log('\n3️⃣ Testing dynamic counts with search context...');
      
      // Test with thirdparty field
      const thirdPartyParams = new URLSearchParams({
        field: 'thirdparty',
        filters: JSON.stringify({ allFields: 'dental' })
      });
      
      const thirdPartyResponse = await fetch(`http://localhost:3000/api/meta/us_pmn/dynamic-counts?${thirdPartyParams}`);
      const thirdPartyResult = await thirdPartyResponse.json();
      
      console.log('Third Party Dynamic Counts:', {
        success: thirdPartyResult.success,
        dataLength: thirdPartyResult.data?.length,
        data: thirdPartyResult.data
      });
      
      // Test with reviewadvisecomm field
      const reviewParams = new URLSearchParams({
        field: 'reviewadvisecomm',
        filters: JSON.stringify({ allFields: 'dental' })
      });
      
      const reviewResponse = await fetch(`http://localhost:3000/api/meta/us_pmn/dynamic-counts?${reviewParams}`);
      const reviewResult = await reviewResponse.json();
      
      console.log('Review Advisory Committee Dynamic Counts:', {
        success: reviewResult.success,
        dataLength: reviewResult.data?.length,
        totalCount: reviewResult.data?.reduce((sum, item) => sum + item.count, 0)
      });
      
      // Compare with full dataset
      console.log('\n4️⃣ Comparing with full dataset...');
      const fullParams = new URLSearchParams({
        field: 'thirdparty',
        filters: JSON.stringify({})
      });
      
      const fullResponse = await fetch(`http://localhost:3000/api/meta/us_pmn/dynamic-counts?${fullParams}`);
      const fullResult = await fullResponse.json();
      
      console.log('Full Dataset Third Party Counts:', {
        success: fullResult.success,
        dataLength: fullResult.data?.length,
        totalCount: fullResult.data?.reduce((sum, item) => sum + item.count, 0)
      });
      
      // Verify the fix
      if (thirdPartyResult.success && fullResult.success) {
        const filteredTotal = thirdPartyResult.data?.reduce((sum, item) => sum + item.count, 0) || 0;
        const fullTotal = fullResult.data?.reduce((sum, item) => sum + item.count, 0) || 0;
        
        console.log('\n🎯 Filter Fix Verification:');
        console.log(`   Filtered count: ${filteredTotal}`);
        console.log(`   Full dataset count: ${fullTotal}`);
        console.log(`   Search result count: ${searchResult.totalCount}`);
        
        if (filteredTotal <= searchResult.totalCount && filteredTotal <= fullTotal) {
          console.log('✅ Progressive filtering is working correctly!');
          console.log('✅ Filter statistics are calculated based on search results, not entire dataset!');
        } else {
          console.log('❌ There may be an issue with the filtering logic');
        }
      }
      
    } else {
      console.log('❌ Search is not working properly');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testWebInterface();
