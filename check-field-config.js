/**
 * Check field configuration for us_pmn database
 */

async function checkFieldConfig() {
  try {
    // Test the API directly
    const response = await fetch('http://localhost:3000/api/meta/us_pmn');
    const result = await response.json();
    
    console.log('API Response:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Field metadata loaded successfully');

      // Check metadata
      if (result.metadata && typeof result.metadata === 'object') {
        console.log(`Total fields: ${Object.keys(result.metadata).length}`);

        // Check if thirdpartyflag exists
        if (result.metadata.thirdpartyflag) {
          console.log('✅ thirdpartyflag field found in metadata');
          console.log('Values:', result.metadata.thirdpartyflag.slice(0, 5));
        } else {
          console.log('❌ thirdpartyflag field NOT found in metadata');
          console.log('Available fields:', Object.keys(result.metadata).slice(0, 10).join(', '));
        }
      } else {
        console.log('❌ No metadata in response');
      }

      // Check field configs
      if (result.fieldConfigs && Array.isArray(result.fieldConfigs)) {
        console.log(`Field configs loaded: ${result.fieldConfigs.length}`);
        const thirdPartyConfig = result.fieldConfigs.find(f => f.fieldName === 'thirdpartyflag');
        if (thirdPartyConfig) {
          console.log('✅ thirdpartyflag field config found:', {
            fieldName: thirdPartyConfig.fieldName,
            displayName: thirdPartyConfig.displayName,
            isFilterable: thirdPartyConfig.isFilterable,
            filterType: thirdPartyConfig.filterType
          });
        } else {
          console.log('❌ thirdpartyflag field config NOT found');
          console.log('Available field configs:', result.fieldConfigs.slice(0, 5).map(f => f.fieldName).join(', '));
        }
      } else {
        console.log('❌ No field configs in response');
      }
    } else {
      console.log('❌ Failed to load field metadata:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkFieldConfig();
