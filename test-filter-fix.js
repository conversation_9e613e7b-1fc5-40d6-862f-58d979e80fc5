/**
 * Test script to verify the filter fix
 * This script tests the progressive filtering functionality
 */

async function testFilterFix() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing Filter Fix...\n');
  
  try {
    // Test 1: Search for "dental" in device name
    console.log('1️⃣ Testing search for "dental"...');
    const searchResponse = await fetch(`${baseUrl}/api/data/us_pmn?allFields=dental&page=1&limit=20`);
    const searchResult = await searchResponse.json();
    
    if (searchResult.success) {
      console.log(`✅ Search results: ${searchResult.totalCount} records found`);
    } else {
      console.log('❌ Search failed:', searchResult.error);
      return;
    }
    
    // Test 2: Get dynamic counts for "thirdparty" with "dental" search applied
    console.log('\n2️⃣ Testing dynamic counts with search context...');
    const filters = { allFields: 'dental' };
    const params = new URLSearchParams({
      field: 'thirdparty',
      filters: JSON.stringify(filters)
    });
    
    const countsResponse = await fetch(`${baseUrl}/api/meta/us_pmn/dynamic-counts?${params}`);
    const countsResult = await countsResponse.json();
    
    console.log('Dynamic counts response:', JSON.stringify(countsResult, null, 2));

    if (countsResult.success) {
      console.log('✅ Dynamic counts with search context:');
      if (countsResult.data && Array.isArray(countsResult.data)) {
        countsResult.data.forEach(item => {
          console.log(`   ${item.value}: ${item.count} records`);
        });

        // Verify that the counts are based on search results, not entire dataset
        const totalFilteredCount = countsResult.data.reduce((sum, item) => sum + item.count, 0);
        console.log(`   Total filtered count: ${totalFilteredCount}`);

        if (totalFilteredCount > 0 && totalFilteredCount <= searchResult.totalCount) {
          console.log('✅ Filter counts are correctly based on search results!');
        } else if (totalFilteredCount === 0) {
          console.log('⚠️ No results found - this might indicate the search context is working (no "dental" records have thirdparty values)');
        } else {
          console.log('❌ Filter counts seem to be based on entire dataset, not search results');
        }
      } else {
        console.log('❌ No data array in response');
      }
    } else {
      console.log('❌ Dynamic counts failed:', countsResult.error);
    }
    
    // Test 3: Test configurable stats with filters
    console.log('\n3️⃣ Testing configurable stats with filters...');
    const statsParams = new URLSearchParams({ allFields: 'dental' });
    const statsResponse = await fetch(`${baseUrl}/api/stats/us_pmn/configurable?${statsParams}`);
    const statsResult = await statsResponse.json();
    
    if (statsResult.success) {
      console.log('✅ Configurable stats with filters:');
      console.log(`   Total: ${statsResult.data.basic.total}`);
      console.log(`   Active: ${statsResult.data.basic.active}`);
      console.log(`   Statistics fields: ${statsResult.data.statistics.length}`);
    } else {
      console.log('❌ Configurable stats failed:', statsResult.error);
    }
    
    console.log('\n🎉 Filter fix test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testFilterFix();
